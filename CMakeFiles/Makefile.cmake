# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/usr/share/cmake-3.28/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.28/Modules/CheckSymbolExists.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake"
  "CMakeFiles/3.28.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.3/CMakeSystem.cmake"
  "CMakeLists.txt"
  "apilib/CMakeLists.txt"
  "apilib/db/CMakeLists.txt"
  "apilib/dep/CMakeLists.txt"
  "apilib/dep/fmt/CMakeLists.txt"
  "apilib/dep/mysql/CMakeLists.txt"
  "apilib/dep/openssl/CMakeLists.txt"
  "apilib/dep/utf8cpp/CMakeLists.txt"
  "apilib/log/CMakeLists.txt"
  "apilib/net/CMakeLists.txt"
  "apilib/util/CMakeLists.txt"
  "cmake/AutoCollect.cmake"
  "cmake/ConfigureBaseTargets.cmake"
  "cmake/Utils.cmake"
  "cmake/macros/ConfigureScripts.cmake"
  "cmake/macros/EnsureVersion.cmake"
  "cmake/macros/FindMySQL.cmake"
  "cmake/macros/FindOpenSSL.cmake"
  "cmake/options.cmake"
  "cmake/showoptions.cmake"
  "src/CMakeLists.txt"
  "src/aibox/CMakeLists.txt"
  "src/correspondserver/CMakeLists.txt"
  "src/gameserver/CMakeLists.txt"
  "src/subgame/CMakeLists.txt"
  "src/subgame/redblack/CMakeLists.txt"
  "vision_data.h.in.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "vision_data.h"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/dep/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/dep/fmt/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/dep/mysql/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/dep/openssl/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/dep/utf8cpp/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/util/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/log/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/net/CMakeFiles/CMakeDirectoryInformation.cmake"
  "apilib/db/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/correspondserver/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/aibox/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/gameserver/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/subgame/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/subgame/redblack/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "apilib/dep/fmt/CMakeFiles/fmt.dir/DependInfo.cmake"
  "apilib/util/CMakeFiles/Util.dir/DependInfo.cmake"
  "apilib/log/CMakeFiles/Log.dir/DependInfo.cmake"
  "apilib/net/CMakeFiles/Net.dir/DependInfo.cmake"
  "apilib/db/CMakeFiles/DataBase.dir/DependInfo.cmake"
  "src/correspondserver/CMakeFiles/CorrespondServer.dir/DependInfo.cmake"
  "src/aibox/CMakeFiles/AI_Box.dir/DependInfo.cmake"
  "src/aibox/CMakeFiles/valgrind_test.dir/DependInfo.cmake"
  "src/gameserver/CMakeFiles/GameServer.dir/DependInfo.cmake"
  "src/subgame/redblack/CMakeFiles/RedBlack.dir/DependInfo.cmake"
  )
