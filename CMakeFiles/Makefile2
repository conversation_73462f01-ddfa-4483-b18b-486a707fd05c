# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /work/Lendy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /work/Lendy

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: apilib/all
all: src/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: apilib/preinstall
preinstall: src/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: apilib/clean
clean: src/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory apilib

# Recursive "all" directory target.
apilib/all: apilib/dep/all
apilib/all: apilib/util/all
apilib/all: apilib/log/all
apilib/all: apilib/net/all
apilib/all: apilib/db/all
.PHONY : apilib/all

# Recursive "preinstall" directory target.
apilib/preinstall: apilib/dep/preinstall
apilib/preinstall: apilib/util/preinstall
apilib/preinstall: apilib/log/preinstall
apilib/preinstall: apilib/net/preinstall
apilib/preinstall: apilib/db/preinstall
.PHONY : apilib/preinstall

# Recursive "clean" directory target.
apilib/clean: apilib/dep/clean
apilib/clean: apilib/util/clean
apilib/clean: apilib/log/clean
apilib/clean: apilib/net/clean
apilib/clean: apilib/db/clean
.PHONY : apilib/clean

#=============================================================================
# Directory level rules for directory apilib/db

# Recursive "all" directory target.
apilib/db/all: apilib/db/CMakeFiles/DataBase.dir/all
.PHONY : apilib/db/all

# Recursive "preinstall" directory target.
apilib/db/preinstall:
.PHONY : apilib/db/preinstall

# Recursive "clean" directory target.
apilib/db/clean: apilib/db/CMakeFiles/DataBase.dir/clean
.PHONY : apilib/db/clean

#=============================================================================
# Directory level rules for directory apilib/dep

# Recursive "all" directory target.
apilib/dep/all: apilib/dep/fmt/all
apilib/dep/all: apilib/dep/mysql/all
apilib/dep/all: apilib/dep/openssl/all
apilib/dep/all: apilib/dep/utf8cpp/all
.PHONY : apilib/dep/all

# Recursive "preinstall" directory target.
apilib/dep/preinstall: apilib/dep/fmt/preinstall
apilib/dep/preinstall: apilib/dep/mysql/preinstall
apilib/dep/preinstall: apilib/dep/openssl/preinstall
apilib/dep/preinstall: apilib/dep/utf8cpp/preinstall
.PHONY : apilib/dep/preinstall

# Recursive "clean" directory target.
apilib/dep/clean: apilib/dep/fmt/clean
apilib/dep/clean: apilib/dep/mysql/clean
apilib/dep/clean: apilib/dep/openssl/clean
apilib/dep/clean: apilib/dep/utf8cpp/clean
.PHONY : apilib/dep/clean

#=============================================================================
# Directory level rules for directory apilib/dep/fmt

# Recursive "all" directory target.
apilib/dep/fmt/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
.PHONY : apilib/dep/fmt/all

# Recursive "preinstall" directory target.
apilib/dep/fmt/preinstall:
.PHONY : apilib/dep/fmt/preinstall

# Recursive "clean" directory target.
apilib/dep/fmt/clean: apilib/dep/fmt/CMakeFiles/fmt.dir/clean
.PHONY : apilib/dep/fmt/clean

#=============================================================================
# Directory level rules for directory apilib/dep/mysql

# Recursive "all" directory target.
apilib/dep/mysql/all:
.PHONY : apilib/dep/mysql/all

# Recursive "preinstall" directory target.
apilib/dep/mysql/preinstall:
.PHONY : apilib/dep/mysql/preinstall

# Recursive "clean" directory target.
apilib/dep/mysql/clean:
.PHONY : apilib/dep/mysql/clean

#=============================================================================
# Directory level rules for directory apilib/dep/openssl

# Recursive "all" directory target.
apilib/dep/openssl/all:
.PHONY : apilib/dep/openssl/all

# Recursive "preinstall" directory target.
apilib/dep/openssl/preinstall:
.PHONY : apilib/dep/openssl/preinstall

# Recursive "clean" directory target.
apilib/dep/openssl/clean:
.PHONY : apilib/dep/openssl/clean

#=============================================================================
# Directory level rules for directory apilib/dep/utf8cpp

# Recursive "all" directory target.
apilib/dep/utf8cpp/all:
.PHONY : apilib/dep/utf8cpp/all

# Recursive "preinstall" directory target.
apilib/dep/utf8cpp/preinstall:
.PHONY : apilib/dep/utf8cpp/preinstall

# Recursive "clean" directory target.
apilib/dep/utf8cpp/clean:
.PHONY : apilib/dep/utf8cpp/clean

#=============================================================================
# Directory level rules for directory apilib/log

# Recursive "all" directory target.
apilib/log/all: apilib/log/CMakeFiles/Log.dir/all
.PHONY : apilib/log/all

# Recursive "preinstall" directory target.
apilib/log/preinstall:
.PHONY : apilib/log/preinstall

# Recursive "clean" directory target.
apilib/log/clean: apilib/log/CMakeFiles/Log.dir/clean
.PHONY : apilib/log/clean

#=============================================================================
# Directory level rules for directory apilib/net

# Recursive "all" directory target.
apilib/net/all: apilib/net/CMakeFiles/Net.dir/all
.PHONY : apilib/net/all

# Recursive "preinstall" directory target.
apilib/net/preinstall:
.PHONY : apilib/net/preinstall

# Recursive "clean" directory target.
apilib/net/clean: apilib/net/CMakeFiles/Net.dir/clean
.PHONY : apilib/net/clean

#=============================================================================
# Directory level rules for directory apilib/util

# Recursive "all" directory target.
apilib/util/all: apilib/util/CMakeFiles/Util.dir/all
.PHONY : apilib/util/all

# Recursive "preinstall" directory target.
apilib/util/preinstall:
.PHONY : apilib/util/preinstall

# Recursive "clean" directory target.
apilib/util/clean: apilib/util/CMakeFiles/Util.dir/clean
.PHONY : apilib/util/clean

#=============================================================================
# Directory level rules for directory src

# Recursive "all" directory target.
src/all: src/correspondserver/all
src/all: src/aibox/all
src/all: src/gameserver/all
src/all: src/subgame/all
.PHONY : src/all

# Recursive "preinstall" directory target.
src/preinstall: src/correspondserver/preinstall
src/preinstall: src/aibox/preinstall
src/preinstall: src/gameserver/preinstall
src/preinstall: src/subgame/preinstall
.PHONY : src/preinstall

# Recursive "clean" directory target.
src/clean: src/correspondserver/clean
src/clean: src/aibox/clean
src/clean: src/gameserver/clean
src/clean: src/subgame/clean
.PHONY : src/clean

#=============================================================================
# Directory level rules for directory src/aibox

# Recursive "all" directory target.
src/aibox/all: src/aibox/CMakeFiles/AI_Box.dir/all
.PHONY : src/aibox/all

# Recursive "preinstall" directory target.
src/aibox/preinstall:
.PHONY : src/aibox/preinstall

# Recursive "clean" directory target.
src/aibox/clean: src/aibox/CMakeFiles/AI_Box.dir/clean
src/aibox/clean: src/aibox/CMakeFiles/valgrind_test.dir/clean
.PHONY : src/aibox/clean

#=============================================================================
# Directory level rules for directory src/correspondserver

# Recursive "all" directory target.
src/correspondserver/all: src/correspondserver/CMakeFiles/CorrespondServer.dir/all
.PHONY : src/correspondserver/all

# Recursive "preinstall" directory target.
src/correspondserver/preinstall:
.PHONY : src/correspondserver/preinstall

# Recursive "clean" directory target.
src/correspondserver/clean: src/correspondserver/CMakeFiles/CorrespondServer.dir/clean
.PHONY : src/correspondserver/clean

#=============================================================================
# Directory level rules for directory src/gameserver

# Recursive "all" directory target.
src/gameserver/all: src/gameserver/CMakeFiles/GameServer.dir/all
.PHONY : src/gameserver/all

# Recursive "preinstall" directory target.
src/gameserver/preinstall:
.PHONY : src/gameserver/preinstall

# Recursive "clean" directory target.
src/gameserver/clean: src/gameserver/CMakeFiles/GameServer.dir/clean
.PHONY : src/gameserver/clean

#=============================================================================
# Directory level rules for directory src/subgame

# Recursive "all" directory target.
src/subgame/all: src/subgame/redblack/all
.PHONY : src/subgame/all

# Recursive "preinstall" directory target.
src/subgame/preinstall: src/subgame/redblack/preinstall
.PHONY : src/subgame/preinstall

# Recursive "clean" directory target.
src/subgame/clean: src/subgame/redblack/clean
.PHONY : src/subgame/clean

#=============================================================================
# Directory level rules for directory src/subgame/redblack

# Recursive "all" directory target.
src/subgame/redblack/all: src/subgame/redblack/CMakeFiles/RedBlack.dir/all
.PHONY : src/subgame/redblack/all

# Recursive "preinstall" directory target.
src/subgame/redblack/preinstall:
.PHONY : src/subgame/redblack/preinstall

# Recursive "clean" directory target.
src/subgame/redblack/clean: src/subgame/redblack/CMakeFiles/RedBlack.dir/clean
.PHONY : src/subgame/redblack/clean

#=============================================================================
# Target rules for target apilib/dep/fmt/CMakeFiles/fmt.dir

# All Build rule for target.
apilib/dep/fmt/CMakeFiles/fmt.dir/all:
	$(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/depend
	$(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=71,72,73,74,75 "Built target fmt"
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/all

# Build rule for subdir invocation for target.
apilib/dep/fmt/CMakeFiles/fmt.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/dep/fmt/CMakeFiles/fmt.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/rule

# Convenience name for target.
fmt: apilib/dep/fmt/CMakeFiles/fmt.dir/rule
.PHONY : fmt

# clean rule for target.
apilib/dep/fmt/CMakeFiles/fmt.dir/clean:
	$(MAKE) $(MAKESILENT) -f apilib/dep/fmt/CMakeFiles/fmt.dir/build.make apilib/dep/fmt/CMakeFiles/fmt.dir/clean
.PHONY : apilib/dep/fmt/CMakeFiles/fmt.dir/clean

#=============================================================================
# Target rules for target apilib/util/CMakeFiles/Util.dir

# All Build rule for target.
apilib/util/CMakeFiles/Util.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
	$(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/depend
	$(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=63,64,65,66,67,68,69,70 "Built target Util"
.PHONY : apilib/util/CMakeFiles/Util.dir/all

# Build rule for subdir invocation for target.
apilib/util/CMakeFiles/Util.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/util/CMakeFiles/Util.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : apilib/util/CMakeFiles/Util.dir/rule

# Convenience name for target.
Util: apilib/util/CMakeFiles/Util.dir/rule
.PHONY : Util

# clean rule for target.
apilib/util/CMakeFiles/Util.dir/clean:
	$(MAKE) $(MAKESILENT) -f apilib/util/CMakeFiles/Util.dir/build.make apilib/util/CMakeFiles/Util.dir/clean
.PHONY : apilib/util/CMakeFiles/Util.dir/clean

#=============================================================================
# Target rules for target apilib/log/CMakeFiles/Log.dir

# All Build rule for target.
apilib/log/CMakeFiles/Log.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
apilib/log/CMakeFiles/Log.dir/all: apilib/util/CMakeFiles/Util.dir/all
	$(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/depend
	$(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=47,48,49,50,51 "Built target Log"
.PHONY : apilib/log/CMakeFiles/Log.dir/all

# Build rule for subdir invocation for target.
apilib/log/CMakeFiles/Log.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 18
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/log/CMakeFiles/Log.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : apilib/log/CMakeFiles/Log.dir/rule

# Convenience name for target.
Log: apilib/log/CMakeFiles/Log.dir/rule
.PHONY : Log

# clean rule for target.
apilib/log/CMakeFiles/Log.dir/clean:
	$(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/clean
.PHONY : apilib/log/CMakeFiles/Log.dir/clean

#=============================================================================
# Target rules for target apilib/net/CMakeFiles/Net.dir

# All Build rule for target.
apilib/net/CMakeFiles/Net.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
apilib/net/CMakeFiles/Net.dir/all: apilib/util/CMakeFiles/Util.dir/all
apilib/net/CMakeFiles/Net.dir/all: apilib/log/CMakeFiles/Log.dir/all
	$(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/depend
	$(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=52,53,54,55,56,57,58 "Built target Net"
.PHONY : apilib/net/CMakeFiles/Net.dir/all

# Build rule for subdir invocation for target.
apilib/net/CMakeFiles/Net.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 25
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/net/CMakeFiles/Net.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : apilib/net/CMakeFiles/Net.dir/rule

# Convenience name for target.
Net: apilib/net/CMakeFiles/Net.dir/rule
.PHONY : Net

# clean rule for target.
apilib/net/CMakeFiles/Net.dir/clean:
	$(MAKE) $(MAKESILENT) -f apilib/net/CMakeFiles/Net.dir/build.make apilib/net/CMakeFiles/Net.dir/clean
.PHONY : apilib/net/CMakeFiles/Net.dir/clean

#=============================================================================
# Target rules for target apilib/db/CMakeFiles/DataBase.dir

# All Build rule for target.
apilib/db/CMakeFiles/DataBase.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
apilib/db/CMakeFiles/DataBase.dir/all: apilib/util/CMakeFiles/Util.dir/all
apilib/db/CMakeFiles/DataBase.dir/all: apilib/log/CMakeFiles/Log.dir/all
	$(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/depend
	$(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=29,30,31,32,33,34,35,36,37,38,39,40 "Built target DataBase"
.PHONY : apilib/db/CMakeFiles/DataBase.dir/all

# Build rule for subdir invocation for target.
apilib/db/CMakeFiles/DataBase.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 30
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/db/CMakeFiles/DataBase.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : apilib/db/CMakeFiles/DataBase.dir/rule

# Convenience name for target.
DataBase: apilib/db/CMakeFiles/DataBase.dir/rule
.PHONY : DataBase

# clean rule for target.
apilib/db/CMakeFiles/DataBase.dir/clean:
	$(MAKE) $(MAKESILENT) -f apilib/db/CMakeFiles/DataBase.dir/build.make apilib/db/CMakeFiles/DataBase.dir/clean
.PHONY : apilib/db/CMakeFiles/DataBase.dir/clean

#=============================================================================
# Target rules for target src/correspondserver/CMakeFiles/CorrespondServer.dir

# All Build rule for target.
src/correspondserver/CMakeFiles/CorrespondServer.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
src/correspondserver/CMakeFiles/CorrespondServer.dir/all: apilib/util/CMakeFiles/Util.dir/all
src/correspondserver/CMakeFiles/CorrespondServer.dir/all: apilib/log/CMakeFiles/Log.dir/all
src/correspondserver/CMakeFiles/CorrespondServer.dir/all: apilib/net/CMakeFiles/Net.dir/all
	$(MAKE) $(MAKESILENT) -f src/correspondserver/CMakeFiles/CorrespondServer.dir/build.make src/correspondserver/CMakeFiles/CorrespondServer.dir/depend
	$(MAKE) $(MAKESILENT) -f src/correspondserver/CMakeFiles/CorrespondServer.dir/build.make src/correspondserver/CMakeFiles/CorrespondServer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=24,25,26,27,28 "Built target CorrespondServer"
.PHONY : src/correspondserver/CMakeFiles/CorrespondServer.dir/all

# Build rule for subdir invocation for target.
src/correspondserver/CMakeFiles/CorrespondServer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 30
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/correspondserver/CMakeFiles/CorrespondServer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : src/correspondserver/CMakeFiles/CorrespondServer.dir/rule

# Convenience name for target.
CorrespondServer: src/correspondserver/CMakeFiles/CorrespondServer.dir/rule
.PHONY : CorrespondServer

# clean rule for target.
src/correspondserver/CMakeFiles/CorrespondServer.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/correspondserver/CMakeFiles/CorrespondServer.dir/build.make src/correspondserver/CMakeFiles/CorrespondServer.dir/clean
.PHONY : src/correspondserver/CMakeFiles/CorrespondServer.dir/clean

#=============================================================================
# Target rules for target src/aibox/CMakeFiles/AI_Box.dir

# All Build rule for target.
src/aibox/CMakeFiles/AI_Box.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
src/aibox/CMakeFiles/AI_Box.dir/all: apilib/util/CMakeFiles/Util.dir/all
src/aibox/CMakeFiles/AI_Box.dir/all: apilib/log/CMakeFiles/Log.dir/all
src/aibox/CMakeFiles/AI_Box.dir/all: apilib/net/CMakeFiles/Net.dir/all
src/aibox/CMakeFiles/AI_Box.dir/all: apilib/db/CMakeFiles/DataBase.dir/all
	$(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/depend
	$(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 "Built target AI_Box"
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/all

# Build rule for subdir invocation for target.
src/aibox/CMakeFiles/AI_Box.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 60
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/CMakeFiles/AI_Box.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/rule

# Convenience name for target.
AI_Box: src/aibox/CMakeFiles/AI_Box.dir/rule
.PHONY : AI_Box

# clean rule for target.
src/aibox/CMakeFiles/AI_Box.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/AI_Box.dir/build.make src/aibox/CMakeFiles/AI_Box.dir/clean
.PHONY : src/aibox/CMakeFiles/AI_Box.dir/clean

#=============================================================================
# Target rules for target src/aibox/CMakeFiles/valgrind_test.dir

# All Build rule for target.
src/aibox/CMakeFiles/valgrind_test.dir/all: src/aibox/CMakeFiles/AI_Box.dir/all
	$(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/valgrind_test.dir/build.make src/aibox/CMakeFiles/valgrind_test.dir/depend
	$(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/valgrind_test.dir/build.make src/aibox/CMakeFiles/valgrind_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=76 "Built target valgrind_test"
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/all

# Build rule for subdir invocation for target.
src/aibox/CMakeFiles/valgrind_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 61
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/aibox/CMakeFiles/valgrind_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/rule

# Convenience name for target.
valgrind_test: src/aibox/CMakeFiles/valgrind_test.dir/rule
.PHONY : valgrind_test

# clean rule for target.
src/aibox/CMakeFiles/valgrind_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/aibox/CMakeFiles/valgrind_test.dir/build.make src/aibox/CMakeFiles/valgrind_test.dir/clean
.PHONY : src/aibox/CMakeFiles/valgrind_test.dir/clean

#=============================================================================
# Target rules for target src/gameserver/CMakeFiles/GameServer.dir

# All Build rule for target.
src/gameserver/CMakeFiles/GameServer.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
src/gameserver/CMakeFiles/GameServer.dir/all: apilib/util/CMakeFiles/Util.dir/all
src/gameserver/CMakeFiles/GameServer.dir/all: apilib/log/CMakeFiles/Log.dir/all
src/gameserver/CMakeFiles/GameServer.dir/all: apilib/net/CMakeFiles/Net.dir/all
src/gameserver/CMakeFiles/GameServer.dir/all: apilib/db/CMakeFiles/DataBase.dir/all
	$(MAKE) $(MAKESILENT) -f src/gameserver/CMakeFiles/GameServer.dir/build.make src/gameserver/CMakeFiles/GameServer.dir/depend
	$(MAKE) $(MAKESILENT) -f src/gameserver/CMakeFiles/GameServer.dir/build.make src/gameserver/CMakeFiles/GameServer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=41,42,43,44,45,46 "Built target GameServer"
.PHONY : src/gameserver/CMakeFiles/GameServer.dir/all

# Build rule for subdir invocation for target.
src/gameserver/CMakeFiles/GameServer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 43
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/gameserver/CMakeFiles/GameServer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : src/gameserver/CMakeFiles/GameServer.dir/rule

# Convenience name for target.
GameServer: src/gameserver/CMakeFiles/GameServer.dir/rule
.PHONY : GameServer

# clean rule for target.
src/gameserver/CMakeFiles/GameServer.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/gameserver/CMakeFiles/GameServer.dir/build.make src/gameserver/CMakeFiles/GameServer.dir/clean
.PHONY : src/gameserver/CMakeFiles/GameServer.dir/clean

#=============================================================================
# Target rules for target src/subgame/redblack/CMakeFiles/RedBlack.dir

# All Build rule for target.
src/subgame/redblack/CMakeFiles/RedBlack.dir/all: apilib/dep/fmt/CMakeFiles/fmt.dir/all
src/subgame/redblack/CMakeFiles/RedBlack.dir/all: apilib/util/CMakeFiles/Util.dir/all
src/subgame/redblack/CMakeFiles/RedBlack.dir/all: apilib/log/CMakeFiles/Log.dir/all
src/subgame/redblack/CMakeFiles/RedBlack.dir/all: apilib/net/CMakeFiles/Net.dir/all
	$(MAKE) $(MAKESILENT) -f src/subgame/redblack/CMakeFiles/RedBlack.dir/build.make src/subgame/redblack/CMakeFiles/RedBlack.dir/depend
	$(MAKE) $(MAKESILENT) -f src/subgame/redblack/CMakeFiles/RedBlack.dir/build.make src/subgame/redblack/CMakeFiles/RedBlack.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/work/Lendy/CMakeFiles --progress-num=59,60,61,62 "Built target RedBlack"
.PHONY : src/subgame/redblack/CMakeFiles/RedBlack.dir/all

# Build rule for subdir invocation for target.
src/subgame/redblack/CMakeFiles/RedBlack.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 29
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 src/subgame/redblack/CMakeFiles/RedBlack.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /work/Lendy/CMakeFiles 0
.PHONY : src/subgame/redblack/CMakeFiles/RedBlack.dir/rule

# Convenience name for target.
RedBlack: src/subgame/redblack/CMakeFiles/RedBlack.dir/rule
.PHONY : RedBlack

# clean rule for target.
src/subgame/redblack/CMakeFiles/RedBlack.dir/clean:
	$(MAKE) $(MAKESILENT) -f src/subgame/redblack/CMakeFiles/RedBlack.dir/build.make src/subgame/redblack/CMakeFiles/RedBlack.dir/clean
.PHONY : src/subgame/redblack/CMakeFiles/RedBlack.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

