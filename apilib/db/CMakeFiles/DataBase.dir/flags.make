# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DASIO_STANDALONE -DBOOST_ASIO_STANDALONE -DDataBase_EXPORTS -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM

CXX_INCLUDES = -I/work/Lendy/apilib -I/work/Lendy/apilib/define -I/work/Lendy/apilib/define/netdef -I/work/Lendy/apilib/dep/asio -I/work/Lendy/apilib/dep/asio/asio -I/work/Lendy/apilib/dep/asio/asio/detail -I/work/Lendy/apilib/dep/asio/asio/detail/impl -I/work/Lendy/apilib/dep/asio/asio/experimental -I/work/Lendy/apilib/dep/asio/asio/experimental/impl -I/work/Lendy/apilib/dep/asio/asio/generic -I/work/Lendy/apilib/dep/asio/asio/generic/detail -I/work/Lendy/apilib/dep/asio/asio/generic/detail/impl -I/work/Lendy/apilib/dep/asio/asio/impl -I/work/Lendy/apilib/dep/asio/asio/ip -I/work/Lendy/apilib/dep/asio/asio/ip/detail -I/work/Lendy/apilib/dep/asio/asio/ip/detail/impl -I/work/Lendy/apilib/dep/asio/asio/ip/impl -I/work/Lendy/apilib/dep/asio/asio/local -I/work/Lendy/apilib/dep/asio/asio/local/detail -I/work/Lendy/apilib/dep/asio/asio/local/detail/impl -I/work/Lendy/apilib/dep/asio/asio/posix -I/work/Lendy/apilib/dep/asio/asio/ssl -I/work/Lendy/apilib/dep/asio/asio/ssl/detail -I/work/Lendy/apilib/dep/asio/asio/ssl/detail/impl -I/work/Lendy/apilib/dep/asio/asio/ssl/impl -I/work/Lendy/apilib/dep/asio/asio/ts -I/work/Lendy/apilib/dep/asio/asio/windows -I/work/Lendy/apilib/net -I/work/Lendy/apilib/net/CMakeFiles -I/work/Lendy/apilib/net/CMakeFiles/Net.dir -I/work/Lendy/apilib/db -I/work/Lendy/apilib/db/CMakeFiles -I/work/Lendy/apilib/db/CMakeFiles/DataBase.dir -I/work/Lendy/apilib/db/CMakeFiles/DataBase.dir/Implementation -I/work/Lendy/apilib/db/Implementation -I/work/Lendy/apilib/log -I/work/Lendy/apilib/log/CMakeFiles -I/work/Lendy/apilib/log/CMakeFiles/Log.dir -I/work/Lendy/apilib/util -I/work/Lendy/apilib/util/CMakeFiles -I/work/Lendy/apilib/util/CMakeFiles/Util.dir -I/work/Lendy/vision_data.h -I/work/Lendy/apilib/dep/fmt -I/usr/local/openssl/include -I/work/Lendy/apilib/dep/utf8cpp -isystem /usr/include/mysql

CXX_FLAGS = -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -fPIC

