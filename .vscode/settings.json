{

  "[cpp]": {
    "editor.fontFamily": "Consolas, 'Courier New', monospace",
    "editor.fontSize": 17,
    "editor.fontLigatures": true,


  },
  "files.associations": {
    "any": "cpp",
    "array": "cpp",
    "atomic": "cpp",
    "bit": "cpp",
    "cctype": "cpp",
    "charconv": "cpp",
    "chrono": "cpp",
    "cinttypes": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "codecvt": "cpp",
    "compare": "cpp",
    "complex": "cpp",
    "concepts": "cpp",
    "condition_variable": "cpp",
    "coroutine": "cpp",
    "csignal": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "list": "cpp",
    "map": "cpp",
    "set": "cpp",
    "string": "cpp",
    "unordered_map": "cpp",
    "unordered_set": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "optional": "cpp",
    "random": "cpp",
    "ratio": "cpp",
    "source_location": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "format": "cpp",
    "fstream": "cpp",
    "future": "cpp",
    "initializer_list": "cpp",
    "iomanip": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "numbers": "cpp",
    "ostream": "cpp",
    "semaphore": "cpp",
    "shared_mutex": "cpp",
    "span": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "stdfloat": "cpp",
    "stop_token": "cpp",
    "streambuf": "cpp",
    "thread": "cpp",
    "cfenv": "cpp",
    "typeindex": "cpp",
    "typeinfo": "cpp",
    "variant": "cpp",
    "bitset": "cpp",
    "regex": "cpp",
    "filesystem": "cpp",
    "cuchar": "cpp"
}
}