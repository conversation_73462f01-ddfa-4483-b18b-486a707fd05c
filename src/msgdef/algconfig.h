#ifndef _ALGCONFIGH
#define _ALGCONFIGH




#include  <iostream>
/*
*******************************参数说明*****************************
A1:						//未戴安全帽
vector<float> thrs;  size=3

thrs[0]:人体识别分数阈值;							(0.0-1.0)
thrs[1]:安全帽识别分数阈值;						(0.0-1.0)
thrs[2]:重叠区域面积在安全帽区域中占比,大于有效。	(0.0-1.0]


A2:                 //未穿反光衣
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;							(0.0-1.0)
thrs[1]:反光衣识别分数阈值;						(0.0-1.0)
thrs[2]:重叠区域面积在反光衣区域中占比,大于有效。	(0.0-1.0]

A3:					 //人员聚集
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;							(0.0-1.0)
thrs[1]:人数超过阈值报警;							[2-10]

A4: //人员徘徊
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;							(0.0-1.0)
thrs[1]:徘徊时间阈值 单位s;						[60 - 10*60]


A5：	//人员计数
vector<float> thrs;  size=1
thrs[0]:人体识别分数阈值;							(0.0-1.0)
mask_points 线段起始点集合
lines_dir   跨线判断集合


A6,		//区域入侵
vector<float> thrs;  size=1
thrs[0]:人体识别分数阈值;					(0.0-1.0)


A7,		//离岗检测
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:允许离开时间阈值 单位s;				(300-600)
std::vector<int> Post_person;              //最少岗位人数


A8,		//睡岗检测
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:睡姿识别分数阈值;					(0.0-1.0)
thrs[2]:允许睡眠时间阈值 单位s;					180


A9:   //抽烟检测
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;							(0.0-1.0)
thrs[1]:吸烟识别分数阈值;							(0.0-1.0)


A10,		//使用手机检测
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:手机识别分数阈值;					(0.0-1.0)


A11,		//跌倒检测
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:跌倒识别分数阈值;					(0.0-1.0)
thrs[2]:重叠区域iou占比,大于有效。	        (0.0-1.0]

A12,		//打架检测
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:打架识别分数阈值;					(0.0-1.0)
thrs[2]:重叠区域iou占比,大于有效。	(0.0-1.0]


B1,        //工服
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:匹配灵敏度。						(0.0-1.0]

B2,        //防护手套
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:手部关键点识别分数阈值;			(0.0-1.0)
thrs[2]:匹配灵敏度。						(0.0-1.0]

B3,        //防护鞋
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:腿部关键点识别分数阈值;			(0.0-1.0)
thrs[2]:匹配灵敏度。						(0.0-1.0]

B4,        //防护眼镜
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:眼睛部关键点识别分数阈值;			(0.0-1.0)
thrs[2]:匹配灵敏度。						(0.0-1.0]

B5,        //防护衣
vector<float> thrs;  size=3
thrs[0]:人体识别分数阈值;					(0.0-1.0)
thrs[1]:躯干关键点识别分数阈值;			(0.0-1.0)
thrs[2]:匹配灵敏度。						(0.0-1.0]




C1,        //人脸识别
vector<float> thrs;  size=2
thrs[0]:人脸分数阈值;						(0.0-1.0)
thrs[1]:匹配灵敏度。						(0.0-1.0]



C2,        //未戴面罩检测
vector<float> thrs;  size=2
thrs[0]:人脸分数阈值;						(0.0-1.0)
thrs[1]:分类灵敏度。						(0.0-1.0]


C3,		//疲劳检测
vector<float> thrs;  size=2
thrs[0]:人脸分数阈值;						(0.0-1.0)
thrs[1]:分类灵敏度。						(0.0-1.0]



D1,        //车型识别
vector<float> thrs;  size=1
thrs[0]:车辆分数阈值;						(0.0-1.0)



D2,        //车辆违停
vector<float> thrs;      size=1
thrs[0]:车辆识别分数阈值;					[0.5-1.0)
thrs[1]:允许停留时间阈值      单位s;		[60 - 10*60]
check_vehicletype:需检测违停类型


D3：	       //车辆计数
vector<float> thrs;  size=1
thrs[0]:人体识别分数阈值;							(0.0-1.0)
mask_points 线段起始点集合
lines_dir   跨线判断集合




D4		//车牌识别
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)


D5		//电瓶车进电梯识别
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)



E1		//烟雾识别
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)

E2		//火焰识别
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)


E3     //灭火器移位
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)

E4     //静电夹移位
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)


E5     //消防占用
vector<float> thrs;  size=1
thrs[0]:灵敏度阈值;							    (0.0-1.0)


E6     //通用OCR识别
vector<float> thrs;  size=1
thrs[0]:识别分数阈值;							(0.0-1.0)



E7	 //双手呈递检测
thrs[0]:人体识别分数阈值;					      (0.0-1.0)
thrs[1]:手臂关键点识别分数阈值;			          (0.0-1.0)
thrs[2]:匹配灵敏度。						     (0.0-1.0]



E8	 //异常穿戴检测
vector<float> thrs;  size=2
thrs[0]:人体识别分数阈值;					 (0.0-1.0)
thrs[1]:异常穿戴识别分数阈值;			     (0.0-1.0)


*/

enum algtype {
    A1 = 1001, // 未戴安全帽   可设立多个或不设立 （每个区域不独立判断）
    A2,        // 未穿反光衣   可设立多个或不设立 （每个区域不独立判断）
    A3,        // 人员聚集	   可设立多个或不设立（每个区域独立判断）
    A4,        // 人员徘徊	   可设立多个或不设立（每个区域不独立判断）     视频测试
    A5,        // 人员计数	   需设立1条或多条（每条线独立判断）			视频测试
    A6,        // 区域入侵	   需设立1个或多个（每个区域不独立判断）
    A7,        // 离岗检测	   需设立1个或多个（每个区域独立判断）         视频测试
    A8,        // 睡岗检测	   需设立1个或多个（每个区域独立判断）		   视频测试
    A9,        // 抽烟检测	   可设立多个或不设立（每个区域不独立判断）
    A10,       // 使用手机	   可设立多个或不设立（每个区域不独立判断）
    A11,       // 跌倒		   可设立多个或不设立（每个区域不独立判断）
    A12,       // 打架		   可设立多个或不设立（每个区域不独立判断）

    B1 = 2001, // 工服		   可设立多个或不设立（每个区域不独立判断）
    B2,        // PPE手套	   可设立多个或不设立（每个区域不独立判断）          框最小限制
    B3,        // PPE防护鞋   可设立多个或不设立（每个区域不独立判断）		    框最小限制
    B4, // PPE防护眼镜 可设立多个或不设立（每个区域不独立判断）      框最小限制+正面
    B5, // PPE躯干	   可设立多个或不设立（每个区域不独立判断）          框最小限制

    C1 = 3001, // 人脸识别      可设立多个或不设立（每个区域不独立判断）
    C2,        // 未戴面罩检测     可设立多个或不设立（每个区域不独立判断）
    C3,        // 疲劳检测		可设立多个或不设立（每个区域不独立判断）
    D1,        // 车型识别		可设立多个或不设立（每个区域不独立判断）
    D2,        // 车辆违停		需设立1个或多个区域（每个区域不独立判断）
    D3,        // 车辆计数		需设立1条或多条（每条线独立判断）
    D4,        // 车牌识别		识别面积最大的车牌
    D5,        // 电瓶车进电梯		可设立多个或不设立（每个区域不独立判断）

    E1 = 4001, // 烟雾识别        可设立多个或不设立（每个区域不独立判断）
    E2,        // 火焰识别	    可设立多个或不设立（每个区域不独立判断）
    E3,        // 灭火器移位      需设立1个或多个（每个区域独立判断）
    E4,        // 静电夹移位	    需设立1个或多个（每个区域独立判断）
    E5,        // 消防占用	    需设立1个或多个（每个区域独立判断）
    E6,        // 通用OCR         需设立1个或多个（每个区域独立判断）（仅支持正矩形）
    E7,        // 双手呈递检测    可设立1个或多个（每个区域不独立判断）
    E8         // 异常穿戴检测    可设立1个或多个（每个区域不独立判断）
};

enum W_Blists { white_list, black_list };

enum restype {
    OK,
    NG,
    ERR

};

enum orientation {
    undefinedir,
    L2R,  // 左到右
    R2L,  // 右到左
    U2D,  // 上到下
    D2U,  // 下到上
    LaRd, // 左加右减
    LdRa, // 左减右加
    UaDd, // 上加下减
    UdDa  // 上减下加

};

enum LineDirection { HORIZONTAL, VERTICAL, DIAGONAL_LTR, DIAGONAL_RTL, UNDEFINED };

enum deepimgtype {
    Customized = 0,
    Face,
    Clothes,
    PPE_Imgs,
    fire_escape

};

enum vehicletype {
    car,
    motor,
    bus,
    truck

};

struct CameraInfo {
    int ID;
    std::string IP;
    std::string RTSP;
    XPACK(O(ID, IP, RTSP));
};
struct CameraInfolist {
    std::vector<CameraInfo> CameraInfoList;
    XPACK(O(CameraInfoList));
};

struct spoint {
    int x;
    int y;

    XPACK(O(x, y));
};

struct sdeepimglib {
    W_Blists W_B_list = white_list;
    deepimgtype dptype;
    std::string path;
    XPACK(O(W_B_list, dptype, path));
};

struct scross_line {
    int line_id;
    spoint line_points1;
    spoint line_points2;
    orientation dir;
    XPACK(O(line_id, line_points1, line_points2, dir));
};

struct sParam {

    algtype algid;
    std::vector<float> thrs;
    sdeepimglib DP_Match_imgs;
    std::vector<scross_line> lines;               // 线段起始点集合
    std::vector<std::vector<spoint>> mask_points; // 区域
    int Post_person;                              // 岗位人数
    std::vector<vehicletype> check_vehicletype;   // 检测违停类型

    int warn_interval = 5;
    int warn_window = 5;
    int warn_thr = 3;
    int warn_level = 1;

    XPACK(O(algid, thrs, DP_Match_imgs, lines, mask_points, Post_person, check_vehicletype, warn_interval, warn_window,
            warn_thr, warn_level));
};

struct sparam_id {
    int cameraid;
    std::vector<sParam> param;
    XPACK(O(cameraid, param));
};

struct sParam_idlist {
    std::vector<sparam_id> algparamidList;
    XPACK(O(algparamidList));
};

struct svehiclebox {
    vehicletype _vehicletype;
    spoint lefttoppoint;
    spoint rightbotpoint;
    XPACK(O(_vehicletype, lefttoppoint, rightbotpoint));
};

struct sline_res {
    int lineid = -1;
    int number = 0;
    XPACK(O(lineid, number));
};

struct sOCRRes {

    std::string text;
    std::vector<spoint> points;
    XPACK(O(text, points));
};


struct sWarn{


    int cameraid;
    algtype algid;
    std::vector<unsigned char> imgbuffer;
    std::string imgpath;
    int level;
    long long  m_nTime;


    std::vector<svehiclebox> vehicle;

    std::vector<sline_res> value_p;
    std::vector<sline_res> value_c;

    std::vector<sOCRRes>License_plate;
    std::vector<sOCRRes>nolocrres;
     XPACK(O(cameraid,algid,imgbuffer,imgpath,level,m_nTime,vehicle,value_p,value_c,License_plate,nolocrres));

};

#endif /* _ALGCONFIGH */