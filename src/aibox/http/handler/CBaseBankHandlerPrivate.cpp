#include "CBaseBankHandlerPrivate.h"
#include <filesystem>
// #include <QtSql>
// #include <QMap>
// #include <QtDebug>

// #include "COptional.h"
// #include "CDBHelper.h"
#include "../../data/CGlobal.h"
#include "CHttpTools.h"
#include "CParam.h"
#include "CTools.h"
// #include "CGlobal.h"
#include "CDirManager.h"
#include "xlnt/xlnt.hpp"
// #include "CTools.h"
// #include "LogMacro.h"
#include "../../ServiceUnits.h"
#include "../../db/DBExports.h"
#include "../../log/Log.h"
#include "../../msgdef/algconfig.h"
#include "../../util/StringFormat.h"
#include "../../util/Timer.h"

#include <cstdio>

// #include "json.hpp"
using namespace LogComm;

struct BankItemInfo {
    std::string eid;
    std::string name;
    std::string image;
    std::string desc;
    std::string param;
};

void CBaseBankHandlerPrivate::sub_group(const httplib::Request &req, httplib::Response &res) {
    std::cout << "CBaseBankHandlerPrivate::sub_group start" << std::endl;
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(group_id);
    if (!group_id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_SUB_GROUP);
    stmt->SetInt32(0, std::stoi(group_id.value()));
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    

    if (query) {

        std::vector<std::string> lastRet;
        do
        {
            DB::Field *field = query->Fetch();
            Json::Value json;
            json["id"] = field[0].GetInt32();
            json["type"] = field[1].GetInt32();
            json["name"] = field[2].GetString();
            json["param"] = field[3].GetString();
            std::string str = CHttpTools::JsonToString(json);

            lastRet.emplace_back(str);
        } while (query->NextRow());

        

        std::string ret = "";
        Util::vectorToString<std::string>(lastRet, ret, ",");
        std::string str = "[" + ret + "]";

        res.set_content(CHttpTools::jsonObj(true, str), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }else{
        res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}

void CBaseBankHandlerPrivate::add_group(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(group_id);
    Req_Read_File(name);
    Req_Read_File(type);
    Req_Read_File(param);

    if (!group_id.valid() || !name.valid() || !type.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    //

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_SUB_GROUP_INSERT);
    stmt->SetInt32(0, std::stoi(group_id.value()));
    stmt->SetInt32(1, std::stoi(type.value()));
    stmt->SetString(2, name.value());
    if (param.valid()) {
        stmt->SetString(3, name.value());
    } else {
        stmt->SetString(3, "{}");
    }

    
    DB::LogonDatabasePool.Query(stmt);
    // AI_BOX_BASE_BANK_SUB_GROUP_ID
    DB::PreparedStatement *stmtId = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_SUB_GROUP_ID);
    auto query = DB::LogonDatabasePool.Query(stmtId);
    if (query) {
        DB::Field *field = query->Fetch();
        int32 id = field[0].GetInt32();
        Json::Value value;
        value["id"] = id;
        std::string str = CHttpTools::JsonToString(value);

        res.set_content(CHttpTools::jsonObj(true, str), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}

void CBaseBankHandlerPrivate::remove_group(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(id);

    if (!id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    //

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_SUB_GROUP_DELETE);
    stmt->SetInt32(0, std::stoi(id.value()));
    DB::LogonDatabasePool.Query(stmt);

    res.set_content(CHttpTools::json(true, "ok"), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

void CBaseBankHandlerPrivate::query(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(page);  // 第几页(如果从第一页开始算，需要-1)
    Req_Read_File(count); // 一页多少条
    Req_Read_File(group);
    Req_Read_File(sub_group);
    Req_Read_File(name);

    if (!page.valid() || !count.valid() || !group.valid() || !sub_group.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    const int32_t vpage = std::stoi(page.value());
    const int32_t vpageCount = std::stoi(count.value());

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_SUB_GROUP_COUNT);
    stmt->SetInt32(0, std::stoi(group.value()));
    stmt->SetInt32(1, std::stoi(sub_group.value()));

    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);

    int32 totalCount = 0;
    if (query) {
        DB::Field *filed = query->Fetch();
        totalCount = filed[0].GetInt32();
    }

    int totalPage = 1;
    totalPage = std::ceil(totalCount / vpageCount);
    if (totalCount < 1) {
        res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
        return;
    }

    std::string condition = "";

    if (group.valid()) {
        auto group_id = fmt::format(" and group_id = {}", group.value());
        condition += group_id;
    }

    if (sub_group.valid()) {
        auto subGroup = fmt::format(" and sub_group_id = {}", sub_group.value());
        condition += subGroup;
    }

    if (name.valid() && !name.value().empty()) {
        auto strName = fmt::format(" and name like '%{1}%'", name.value());
        condition += strName;
    }
    int offset = (vpageCount * (vpage - 1));
    if (offset<0) {
        offset = 0;
    }

    auto strLimit = fmt::format(" LIMIT {0} OFFSET {1}", vpageCount, offset);

    // std::string sql = fmt::format("select count(id) from base_bank_table where `enable`=1 and "
    //                               "`group_id` = {0}  and  `sub_group_id` = {1} {2} {3}",
    //                               std::stoi(group.value()), std::stoi(sub_group.value()), condition, strLimit);

    std::string txt2("select `id`,`eid`,`name`,`image`,`param`,`describe`,`dtime` "
                     "from base_bank_table where enable=1 ");

    if (!condition.empty()) {
        txt2 += condition;
    }
    txt2 += strLimit;

    DB::QueryResult dataQuery = DB::LogonDatabasePool.Query(txt2.c_str());
    Json::Value value;
    if (dataQuery) {
        std::string strErr = "";
        do
        {
            try
            {
                DB::Field *field = dataQuery->Fetch();
                Json::Value json;
                json["id"] = field[0].GetInt32();
                json["eid"] = field[1].GetString();
                json["name"] = field[2].GetString();
                json["image"] = field[3].GetString();

                strErr.clear();
                json["param"] = Util::stringToJson(field[4].GetString(), strErr);
                json["describe"] = field[5].GetString();
                json["dtime"] = field[6].GetInt32();
                value["list"].append(json);
            }
            catch(const std::exception& e)
            {
                std::cerr << e.what() << '\n';
            }
            
            

        } while (dataQuery->NextRow());

        

        value["page"] = vpage;
        value["totalPage"] = totalPage;
        value["count"] = totalCount;
        // value["list"] = totalCount;
        auto ret = CHttpTools::JsonToString(value);

        res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    } else {
        res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}

void CBaseBankHandlerPrivate::edit(const httplib::Request &req, httplib::Response &res,
                                   const httplib::ContentReader &content_reader) {
    if (!CHttpTools::checkSession(req, res))
        return;

    if (req.is_multipart_form_data()) {
        XGlobal_Get(CDirManager, pDir, Dir);

        bool is_file = false;
        std::map<std::string, std::string> info;
        std::string name;
        std::string _file;
        std::string file_fullpath;

        // 1.先拿到 file 信息
        std::ofstream outfile;
        content_reader(
            [&](const httplib::MultipartFormData &file) {
                if (file.filename.empty()) {
                    is_file = false;
                    name = file.name;
                    return true;
                }
                name.clear();
                is_file = true;
                _file = file.filename;
                std::string fullPath = fmt::format("{0}/{1}", pDir->getPath(CDirManager::BankPath), _file);

                file_fullpath = fullPath;
                outfile.open(file_fullpath, std::ifstream::binary | std::ifstream::trunc);
                return outfile.is_open();
            },
            // 2.再流式读取
            [&](const char *data, size_t data_length) {
                if (!is_file) { // 如果不是文件，存储属性
                    info[name] = std::string(data, (int)data_length);
                    // info.insert(name, QByteArray(data, (int)data_length));
                } else if (outfile.is_open()) { // 如果是文件  写入
                    if (data_length > 0) {
                        outfile.write(data, data_length);
                    } else {
                        outfile.close();
                    }
                }
                return true;
            });

        std::string newFile = "";
        if (_file != "") {
            newFile += fmt::format("{0}/{1}", info["group"], _file);
        }

        if (!file_fullpath.empty()) {
            // 3.文件重命名
            std::filesystem::path path = std::filesystem::path(pDir->getPath(CDirManager::BankPath)) / info["group"];

            auto imgtemp = info["group"].c_str();
            std::cout << "edit -- path:" << path << "  file_fullpath:" << file_fullpath << "  imgtemp:" << imgtemp
                      << std::endl;
            if (!std::filesystem::exists(path)) {
                
                std::filesystem::create_directories(path);
            }

            std::string newPath = fmt::format("{}/{}", pDir->getPath(CDirManager::BankPath), newFile);
            std::cout<<"-------newPath-------"<<newPath<<"  file_fullpath:"<<file_fullpath<<std::endl;
            std::filesystem::rename(file_fullpath, newPath);
        }

        COptional<int32_t> id;

        if (info.find("id") != info.end()) {
            id = std::stoi(info["id"]);
        }

        std::string condition;

        if (info.find("eid") != info.end()) {
            condition += fmt::format(" `eid`='{}'", info["eid"].c_str());
        }

        if (info.find("name") != info.end()) {

            condition += fmt::format(" , `name`='{}'", info["name"].c_str());
        }

        if (info.find("describe") != info.end()) {
            condition += fmt::format(" , `describe`='{}'", info["describe"].c_str());
        }

        if (info.find("param") != info.end()) {
            condition += fmt::format(" , `param`='{}'", info["param"].c_str());
        }

        if (newFile != "") {
            auto img = pDir->getSubPath(CDirManager::BankPath) + newFile;

            condition += fmt::format(" , image='{}'", img);
        }

        // update base_bank_table set ? where `id`= ? and `enable`=1
        std::string sql =
            fmt::format("update base_bank_table set {} where `id`= {} and `enable`=1", condition, id.value());
        DB::LogonDatabasePool.Query(sql.c_str());

        
        res.set_content(CHttpTools::json(true, u8"修改成功"), HttpContentType::json);
        

    } else {
        res.set_content(CHttpTools::json(false, u8"参数错误"), HttpContentType::json);
    }

    res.status = httplib::StatusCode::OK_200;
    return;

    // COptional<int32_t> id;  //告警类型
    // if(req.has_file("id")) {
    //     id = std::stoi(req.get_file_value("id").content);
    // }

    // if(!id.valid()) {
    //     res.set_content(CHttpTools::json(false, u8"缺少id参数"),
    //     HttpContentType::json); return;
    // }

    // QStringList condition;
    // QList<QVariant> args;

    // if(req.has_file("eid")) {
    //     std::string eid = req.get_file_value("eid").content;

    //     condition << "eid=?";
    //     args.append(QVariant(QString::fromStdString(eid)));
    // }

    // if(req.has_file("name")) {
    //     std::string name = req.get_file_value("name").content;

    //     condition << "name=?";
    //     args.append(QVariant(QString::fromStdString(name)));
    // }

    // if(req.has_file("describe")) {
    //     std::string describe = req.get_file_value("describe").content;

    //     condition << "describe=?";
    //     args.append(QVariant(QString::fromStdString(describe)));
    // }

    // if(req.has_file("param")) {
    //     std::string param = req.get_file_value("param").content;

    //     condition << "param=?";
    //     args.append(QVariant(QString::fromStdString(param)));
    // }
    // if(req.has_file("file")){
    //     std::string file = req.get_file_value("file").content;

    // }

    // QString txt = QString("update base_bank_table set %1 "
    //                       "where `id`=? and `enable`=1")
    //                   .arg(condition.join(','));

    // QList<QVariant> bindValues;
    // bindValues << args;
    // bindValues << (id.value());

    // auto future = XDBHelper.execSql(txt, bindValues);
    // auto query  = future.get();

    // if(query)
    // {
    //     if(query.value().numRowsAffected() > 0)
    //         res.set_content(CHttpTools::json(true, u8"修改成功"),
    //         HttpContentType::json);
    //     else
    //         res.set_content(CHttpTools::json(false, u8"修改失败"),
    //         HttpContentType::json);

    //     res.status = httplib::StatusCode::OK_200;
    // }
}

void CBaseBankHandlerPrivate::remove(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(id);

    if (!id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少id参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_DELETE);
    stmt->SetInt32(0, std::stoi(id.value()));

    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (!query) {
        res.set_content(CHttpTools::json(false, u8"查询失败"), HttpContentType::json);

    } else {
        res.set_content(CHttpTools::json(true, u8"删除成功"), HttpContentType::json);
    }

    res.status = httplib::StatusCode::OK_200;
}

void CBaseBankHandlerPrivate::add(const httplib::Request &req, httplib::Response &res,
                                  const httplib::ContentReader &content_reader) {
    if (!CHttpTools::checkSession(req, res))
        return;

    if (req.is_multipart_form_data()) {
        XGlobal_Get(CDirManager, pDir, Dir);

        bool is_file = false;
        std::map<std::string, std::string> info;
        std::string name;
        std::string _file;
        std::string file_fullpath;

        // 1.先拿到 file 信息
        std::ofstream outfile;
        content_reader(
            [&](const httplib::MultipartFormData &file) {
                if (file.filename.empty()) {
                    is_file = false;
                    name = file.name;
                    return true;
                }
                name.clear();
                is_file = true;
                _file = file.filename;
                std::string fullPath = fmt::format("{0}/{1}", pDir->getPath(CDirManager::BankPath), _file);

                file_fullpath = fullPath;
                outfile.open(file_fullpath, std::ifstream::binary | std::ifstream::trunc);
                return outfile.is_open();
            },
            // 2.再流式读取
            [&](const char *data, size_t data_length) {
                if (!is_file) { // 如果不是文件，存储属性
                    info[name] = std::string(data, (int)data_length);
                } else if (outfile.is_open()) { // 如果是文件  写入
                    if (data_length > 0) {
                        outfile.write(data, data_length);
                    } else {
                        outfile.close();
                    }
                }
                return true;
            });

        const std::string newFile = fmt::format("{0}/{1}", info["group"], _file);
        LOG_INFO("server.http", "file_fullpath:%s", file_fullpath.c_str());
        std::string newPath = file_fullpath;
        if (!file_fullpath.empty()) {
            std::filesystem::path path = std::filesystem::path(pDir->getPath(CDirManager::BankPath)) / info["group"];
            std::cout << "path:" << path << " group:" << info["group"]<<std::endl;
            if (!std::filesystem::exists(path)) {
                std::filesystem::create_directories(path);
            }
            newPath = fmt::format("{0}/{1}", pDir->getPath(CDirManager::BankPath), newFile);
            std::filesystem::rename(file_fullpath, newPath);
        }
        LOG_INFO("server.http", "newPath:%s", newPath.c_str());
        DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_INSERT);

        if (info.find("group") == info.end()) {
            std::cout << "-----group------"<<std::endl;
            /* code */
        }
        if (info.find("sub_group") == info.end()) {
            std::cout << "-----sub_group------" << std::endl;
            /* code */
        }
        if (info.find("eid") == info.end()) {
            std::cout << "-----eid------" << std::endl;
            /* code */
        }
        if (info.find("name") == info.end()) {
            std::cout << "-----name------" << std::endl;
            /* code */
        }
        if (info.find("describe") == info.end()) {
            std::cout << "-----describe------" << std::endl;
            /* code */
        }
        if (info.find("param") == info.end()) {
            std::cout << "-----param------" << std::endl;
            /* code */
        }

        std::cout << "-----newFile------:" << newFile << std::endl;
        stmt->SetInt32(0, std::stoi(info["group"]));
        stmt->SetInt32(1, std::stoi(info["sub_group"]));
        stmt->SetString(2, info["eid"]);
        stmt->SetString(3, info["name"]);
        stmt->SetString(4, pDir->getSubPath(CDirManager::BankPath) + "/" + newFile);
        stmt->SetString(5, info["describe"]);
        if (info.find("param") != info.end())
            stmt->SetString(6, info["param"]);
        else
            stmt->SetString(6, "{}");
        stmt->SetInt64(7, DB::getCurrentTimeSec());
        stmt->SetInt32(8, 1);
        DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);

        res.set_content(CHttpTools::json(true, u8"添加成功"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;



        sdeepimglib slib;
        slib.W_B_list = white_list;
        slib.dptype = (deepimgtype)std::stoi(info["sub_group"]);
        slib.path = newFile;

        SrvUnitsMgr->PostControlRequest(Logon::UDC_MDM_MB_SDEEPIMGLIB, &slib, sizeof(sdeepimglib));

    } else {
        res.set_content(CHttpTools::json(false, u8"参数错误"), HttpContentType::json);
    }
}

void CBaseBankHandlerPrivate::addZip(const httplib::Request &req, httplib::Response &res,
                                     const httplib::ContentReader &content_reader) {
    if (!CHttpTools::checkSession(req, res))
        return;

    if (req.is_multipart_form_data()) {
        XGlobal_Get(CDirManager, pDir, Dir);

        bool is_file = false;
        std::string fullPath;
        std::map<std::string, std::string> info;
        std::string name;

        std::ofstream outfile;
        // 1.先拿到file 信息
        content_reader(
            [&](const httplib::MultipartFormData &file) {
                if (file.filename.empty()) {
                    is_file = false;
                    name = file.name;
                    return true;
                }
                name.clear();
                is_file = true;
                std::cerr << "\tupload read " << file.filename << "\t" << file.content << std::endl;

                fullPath = fmt::format("{0}/{1}", pDir->getPath(CDirManager::BankTmpPath), file.filename);

                auto file_fullpath = fullPath;
                outfile.open(file_fullpath, std::ifstream::binary | std::ifstream::trunc);
                return outfile.is_open();
            },
            [&](const char *data, size_t data_length) // 2.再流式读取
            {
                if (!is_file) { // 如果不是文件，存储属性
                    info[name] = std::string(data, (int)data_length);
                } else if (outfile.is_open()) { // 如果是文件  写入
                    if (data_length > 0) {
                        outfile.write(data, data_length);
                    } else {
                        outfile.flush();
                        outfile.close();
                    }
                }
                return true;
            });

        if (!fullPath.empty()) {
            // https://www.linuxprobe.com/linux-unzip.html
            // 解压 -o覆盖   -q禁止打印  -d指定解压路径  -P密码
            const std::string unzip_cmd =
                fmt::format("unzip -o -q {0} -d {1}", fullPath, pDir->getPath(CDirManager::BankTmpPath));

            system(unzip_cmd.data());

            const std::string source_path = pDir->getPath(CDirManager::BankTmpPath);
            const std::string dest_path = pDir->getPath(CDirManager::BankPath) + "/" + info["group"] + "/";
            CTools::checkPath(dest_path);
            // 移动
            const std::string mv_cmd = "mv -f " + source_path + "/template/*.png " + dest_path;
            system(mv_cmd.data());

            // 读取配置文件
            std::vector<BankItemInfo> list;
            const std::string _full_file = source_path + "/template/template.xlsx";
            if (std::filesystem::exists(_full_file.c_str())) {
                xlnt::workbook wb;
                wb.load(_full_file);
                auto ws = wb.active_sheet();
                const std::string dest_not_root_path =
                    pDir->getSubPath(CDirManager::BankPath) + "/" + info["group_id"] + "/";
                int index = 0;
                for (const auto row : ws.rows(false)) // 第0行时表头 不需要读取
                {
                    if (index++ == 0)
                        continue;

                    const size_t colCount = row.length();
                    if (colCount >= 5) {
                        BankItemInfo item;
                        item.eid = row[0].to_string();
                        item.name = row[1].to_string();
                        item.image = dest_not_root_path + row[2].to_string();
                        item.desc = row[3].to_string();
                        item.param = row[4].to_string();
                        list.emplace_back(item);
                    }
                }
            }

            // 删除
            const std::string rm_cmd = "rm -rf " + source_path + "/template*";
            system(rm_cmd.data());

            // 插入数据库
            if (!list.empty()) {
                long dt = DB::getCurrentTimeSec();
                DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_INSERT);
                stmt->SetInt32(0, std::stoi(info["group_id"]));
                stmt->SetInt32(1, std::stoi(info["sub_group_id"]));
                stmt->SetString(2, info["eid"]);
                stmt->SetString(3, info["name"]);
                stmt->SetString(4, info["image"]);
                stmt->SetString(5, info["describe"]);
                stmt->SetString(6, info["param"]);
                stmt->SetInt64(7, dt);
                stmt->SetInt32(8, 1);
                DB::LogonDatabasePool.Query(stmt);
            }
        }

        res.set_content(CHttpTools::json(true, u8"ok"), HttpContentType::json);
    } else {
        res.set_content(CHttpTools::json(false, u8"参数错误"), HttpContentType::json);
    }
}

void CBaseBankHandlerPrivate::load(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    COptional<int> group_id;
    COptional<int> sub_group_id;

    if (req.has_file("group_id")) {
        group_id = std::stoi(req.get_file_value("group_id").content);
    }

    if (req.has_file("sub_group_id")) {
        sub_group_id = std::stoi(req.get_file_value("sub_group_id").content);
    }

    if (!group_id.valid() || !sub_group_id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_LOAD);
    stmt->SetInt32(0, group_id.value());
    stmt->SetInt32(1, sub_group_id.value());
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (query) {
        res.set_content(CHttpTools::json(true, u8"加载成功"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}

bool CBaseBankHandlerPrivate::queryFromSubGroup(int sub_group_id, std::string &ret) {
    // if(sub_group_id < 1) {
    //     return false;
    // }

    bool bRet = false;
    // QList<QVariant> bindValues;
    // QString txt("select image from base_bank_table "
    //             "where enable=1 and sub_group_id=?;");

    // bindValues << sub_group_id;

    // auto future = XDBHelper.execSql(txt, bindValues);
    // auto query  = future.get();

    // if(query)
    // {
    //     XGlobal_Get(CDirManager, pDir, Dir);
    //     ret = "[]";
    //     QStringList lastRet;
    //     while(query.value().next()) {
    //         QString str = query.value().value(0).toString();

    //         const QString file = QString("%1/%2")
    //                 .arg(pDir->rootPath())
    //                 .arg(str);

    //         lastRet.append("\"" + file + "\"");
    //     }
    //     QString str = lastRet.join(',');
    //     ret = QString(R"({"data":[%1]})")
    //               .arg(str)
    //               .toStdString();

    //     bRet = true;
    // }
    return bRet;
}

void CBaseBankHandlerPrivate::batchDelete(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(ids);

    if (!ids.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }
    const std::string sIds = ids.value();
    std::string err = "";
    Json::Value json = Util::stringToJson(ids, err);
    if (!err.empty()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
    }

    std::string s = "";
    for (auto item : json) {
        s += item.asString() + ",";
    }
    s.erase(s.end() - 1);

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_BASE_BANK_BATCH_REMOVE);

    stmt->SetString(0, s);
    res.set_content(CHttpTools::json(true, u8"删除成功"), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}
